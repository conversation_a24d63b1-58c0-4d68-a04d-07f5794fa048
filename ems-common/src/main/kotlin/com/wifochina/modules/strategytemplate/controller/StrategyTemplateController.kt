package com.wifochina.modules.strategytemplate.controller

import com.baomidou.mybatisplus.core.metadata.IPage
import com.wifochina.common.constants.ErrorResultCode
import com.wifochina.common.exception.ServiceException
import com.wifochina.common.log.Log
import com.wifochina.common.page.PageBean
import com.wifochina.common.page.Result
import com.wifochina.modules.log.OperationType
import com.wifochina.modules.log.detailresolveservice.StrategyTemplateItemLogDetailService
import com.wifochina.modules.log.detailresolveservice.StrategyTemplateLogDetailService
import com.wifochina.modules.oauth.util.WebUtils
import com.wifochina.modules.strategytemplate.request.StrategyImportWithMonthRequest
import com.wifochina.modules.strategytemplate.request.StrategyTemplateCopyRequest
import com.wifochina.modules.strategytemplate.request.StrategyTemplateItemRequest
import com.wifochina.modules.strategytemplate.request.StrategyTemplatePageRequest
import com.wifochina.modules.strategytemplate.request.StrategyTemplateRequest
import com.wifochina.modules.strategytemplate.service.StrategyTemplateItemService
import com.wifochina.modules.strategytemplate.service.StrategyTemplateService
import com.wifochina.modules.strategytemplate.vo.StrategyTemplateVo
import io.github.oshai.kotlinlogging.KotlinLogging
import io.swagger.annotations.Api
import io.swagger.annotations.ApiOperation
import org.springframework.boot.actuate.autoconfigure.metrics.MetricsProperties.Web
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

private val log = KotlinLogging.logger { }

/**
 * Created on 2025/4/17 17:13.
 * <AUTHOR>
 */
@RequestMapping("/strategyTemplate")
@RestController
@Api(tags = ["策略模版"])
class StrategyTemplateController(
    val strategyTemplateService: StrategyTemplateService, val strategyTemplateItemService: StrategyTemplateItemService
) {


    @PostMapping("/page")
    @ApiOperation("分页查询接口")
    fun page(@RequestBody pageBean: StrategyTemplatePageRequest): Result<IPage<StrategyTemplateVo>> {
        val pages = strategyTemplateService.getPage(pageBean.projectId, pageBean)
        return Result.success(pages)
    }

    @GetMapping("/getTemplates")
    @ApiOperation("查询模版s")
    fun getTemplates(): Result<List<StrategyTemplateVo>> {
        return Result.success(
            strategyTemplateService.getTemplates(WebUtils.projectId.get())
        )
    }

    @PostMapping("/add")
    @ApiOperation("新增策略模版")
    @PreAuthorize("hasAuthority('/strategy/time/add')")
    @Log(module = "STRATEGY_TEMPLATE", methods = "STRATEGY_TEMPLATE_ADD", type = OperationType.ADD)
    fun addTemplate(@RequestBody strategyTemplateRequest: StrategyTemplateRequest): Result<Unit> {
        strategyTemplateService.addTemplate(strategyTemplateRequest)
        return Result.success()
    }

    @PostMapping("/copy")
    @ApiOperation("复制模版")
    @PreAuthorize("hasAuthority('/strategy/time/add')")
    @Log(module = "STRATEGY_TEMPLATE", methods = "STRATEGY_TEMPLATE_ADD", type = OperationType.ADD)
    fun copyTemplate(@RequestBody strategyTemplateCopyRequest: StrategyTemplateCopyRequest): Result<Unit> {
        strategyTemplateService.copyTemplate(strategyTemplateCopyRequest)
        return Result.success()
    }


    @PostMapping("/update")
    @ApiOperation("修改策略模版")
    @PreAuthorize("hasAuthority('/strategy/time/add')")
    @Log(module = "STRATEGY_TEMPLATE", methods = "STRATEGY_TEMPLATE_UPDATE", type = OperationType.UPDATE)
    fun updateTemplate(@RequestBody strategyTemplateRequest: StrategyTemplateRequest): Result<Unit> {
        strategyTemplateRequest.projectId = WebUtils.projectId.get()
        strategyTemplateService.updateTemplate(strategyTemplateRequest)
        return Result.success()
    }


    @PostMapping("/delete/{id}")
    @PreAuthorize("hasAuthority('/strategy/time/add')")
    @ApiOperation("删除策略模版")
    @Log(
        module = "STRATEGY_TEMPLATE",
        methods = "STRATEGY_TEMPLATE_DEL",
        type = OperationType.DEL,
        logDetailServiceClass = StrategyTemplateLogDetailService::class
    )
    fun deleteTemplate(@PathVariable id: Long): Result<Unit> {
        strategyTemplateService.deleteTemplate(id)
        return Result.success()
    }


    @PostMapping("/itemAdd")
    @PreAuthorize("hasAuthority('/strategy/time/add')")
    @ApiOperation("策略item新增")
    @Log(module = "STRATEGY_TEMPLATE", methods = "STRATEGY_TEMPLATE_ITEM_ADD", type = OperationType.ADD)
    fun itemAdd(@RequestBody strategyTemplateItemRequest: StrategyTemplateItemRequest): Result<Unit> {
        strategyTemplateService.getById(strategyTemplateItemRequest.templateId)?.let {
            strategyTemplateItemService.addItem(it.id!!, strategyTemplateItemRequest)
        } ?: run {
            //模版不存在
            throw ServiceException(ErrorResultCode.ILLEGAL_ARGUMENT.value())
        }
        return Result.success()
    }

    @PostMapping("/itemUpdate")
    @PreAuthorize("hasAuthority('/strategy/time/add')")
    @ApiOperation("策略item编辑")
    @Log(module = "STRATEGY_TEMPLATE", methods = "STRATEGY_TEMPLATE_ITEM_UPDATE", type = OperationType.UPDATE)
    fun itemUpdate(@RequestBody strategyTemplateItemRequest: StrategyTemplateItemRequest): Result<Unit> {
        strategyTemplateItemRequest.id?.let {
            strategyTemplateItemService.updateItem(strategyTemplateItemRequest)
        } ?: run {
            //需要提供更新的item的id
            throw ServiceException(ErrorResultCode.ILLEGAL_ARGUMENT.value())
        }
        return Result.success()
    }

    @PostMapping("/itemDelete")
    @ApiOperation("策略item删除")
    @PreAuthorize("hasAuthority('/strategy/time/add')")
    @Log(
        module = "STRATEGY_TEMPLATE",
        methods = "STRATEGY_TEMPLATE_ITEM_DEL",
        type = OperationType.DEL,
        logDetailServiceClass = StrategyTemplateItemLogDetailService::class
    )
    fun itemDelete(@RequestBody strategyTemplateItemRequest: StrategyTemplateItemRequest): Result<Unit> {
        strategyTemplateItemRequest.id?.let {
            strategyTemplateItemRequest.projectId = WebUtils.projectId.get()
            strategyTemplateItemService.deleteItem(strategyTemplateItemRequest)
        } ?: run {
            //需要提供删除的item的id
            throw ServiceException(ErrorResultCode.ILLEGAL_ARGUMENT.value())
        }
        return Result.success()
    }


}