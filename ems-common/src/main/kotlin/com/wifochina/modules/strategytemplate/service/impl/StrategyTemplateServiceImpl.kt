package com.wifochina.modules.strategytemplate.service.impl

import com.baomidou.mybatisplus.core.metadata.IPage
import com.baomidou.mybatisplus.extension.kotlin.KtQueryWrapper
import com.baomidou.mybatisplus.extension.plugins.pagination.Page
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl
import com.wifochina.common.constants.ErrorResultCode
import com.wifochina.common.exception.ServiceException
import com.wifochina.common.page.PageBean
import com.wifochina.modules.oauth.util.WebUtils
import com.wifochina.modules.strategy.service.StrategyService
import com.wifochina.modules.strategytemplate.converter.StrategyTemplateItemControlConverter
import com.wifochina.modules.strategytemplate.converter.StrategyTemplateConverter
import com.wifochina.modules.strategytemplate.converter.StrategyTemplateItemConverter
import com.wifochina.modules.strategytemplate.entity.StrategyDayTemplateBindEntity
import com.wifochina.modules.strategytemplate.entity.StrategyMonthTemplateBindEntity
import com.wifochina.modules.strategytemplate.entity.StrategyTemplateEntity
import com.wifochina.modules.strategytemplate.mapper.StrategyDayTemplateBindMapper
import com.wifochina.modules.strategytemplate.mapper.StrategyMonthTemplateBindMapper
import com.wifochina.modules.strategytemplate.mapper.StrategyTemplateMapper
import com.wifochina.modules.strategytemplate.request.StrategyTemplateCopyRequest
import com.wifochina.modules.strategytemplate.request.StrategyTemplateItemRequest
import com.wifochina.modules.strategytemplate.request.StrategyTemplateRequest
import com.wifochina.modules.strategytemplate.service.StrategyTemplateItemControlService
import com.wifochina.modules.strategytemplate.service.StrategyTemplateItemService
import com.wifochina.modules.strategytemplate.service.StrategyTemplateService
import com.wifochina.modules.strategytemplate.vo.StrategyTemplateVo
import io.github.oshai.kotlinlogging.KotlinLogging
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.LocalTime

private val logger = KotlinLogging.logger { }

/**
 * Created on 2025/4/17 18:22.
 * <AUTHOR>
 */

@Service
@Transactional(rollbackFor = [Exception::class])

class StrategyTemplateServiceImpl(
    val strategyService: StrategyService,
    val strategyTemplateItemService: StrategyTemplateItemService,
    val strategyTemplateItemControlService: StrategyTemplateItemControlService,
    val strategyDayTemplateBindMapper: StrategyDayTemplateBindMapper,
    val strategyMonthTemplateBindMapper: StrategyMonthTemplateBindMapper
) : ServiceImpl<StrategyTemplateMapper, StrategyTemplateEntity>(), StrategyTemplateService {


    override fun copyTemplate(strategyTemplateCopyRequest: StrategyTemplateCopyRequest) {
        // 可空 可能是管理端复制
        val fromProjectId = strategyTemplateCopyRequest.fromProjectId
        strategyTemplateCopyRequest.copyId?.let { copyId ->
            val strategyTemplateEntityCopy = getById(copyId)
            strategyTemplateEntityCopy.id = null
            strategyTemplateEntityCopy.templateName =
                    //管理端导入 就使用原始模版id
                strategyTemplateCopyRequest.templateName ?: strategyTemplateEntityCopy.templateName
            strategyTemplateEntityCopy.projectId = strategyTemplateCopyRequest.toProjectId
            strategyTemplateEntityCopy.electricPriceType = strategyTemplateCopyRequest.electricPriceType
            strategyTemplateEntityCopy.electricPriceArea = strategyTemplateCopyRequest.electricPriceArea
            strategyTemplateEntityCopy.electricPriceSpan = strategyTemplateCopyRequest.electricPriceSpan
            strategyTemplateEntityCopy.country = strategyTemplateCopyRequest.country
            save(strategyTemplateEntityCopy)
            // itemControl
            val itemMap = strategyTemplateItemService.selectItemByTemplateIdsAll(fromProjectId, listOf(copyId))
            itemMap[copyId]?.let { strategyItems ->
                val itemControlIds =
                    strategyItems.filter { it.itemControlId != null }.map { it.itemControlId!! }.toList()
                val itemControlMap = strategyTemplateItemControlService.selectControlEntityByIds(itemControlIds)
                strategyItems.forEach { strategyItem ->
                    strategyItem.apply {
                        id = null
                        //new itemTemplateId
                        templateId = strategyTemplateEntityCopy.id
                        this.projectId = strategyTemplateCopyRequest.toProjectId
                    }
                    strategyItem.itemControlId?.let { oldItemControlId ->
                        itemControlMap[oldItemControlId]?.let { itemControlEntity ->
                            itemControlEntity.id = null
                            strategyTemplateItemControlService.save(itemControlEntity)
                            //new itemControlId
                            strategyItem.itemControlId = itemControlEntity.id
                        }
                    }
                }
                strategyTemplateItemService.saveBatch(strategyItems)
            }
        }
    }

    /**
     * 创建模版
     * 1.新增模版主体
     * 2.模版的策略s 绑定模版id
     */
    override fun addTemplate(strategyTemplateRequest: StrategyTemplateRequest) {
        //check time 是否有重叠
        validateNoOverlap(strategyTemplateRequest.strategyTemplateItemRequests)
        //1.保存模版主体
        val strategyTemplateEntity = StrategyTemplateConverter.INSTANCE.request2Entity(strategyTemplateRequest)
        this.save(strategyTemplateEntity)
        //2.处理 子项目 strategy template Item , 绑定模版templateId
        strategyTemplateRequest.strategyTemplateItemRequests.forEach { itemRequest ->
            val strategyItemControlEntity = itemRequest.itemControl?.let { itemControlRequest ->
                val strategyItemControlEntity =
                    StrategyTemplateItemControlConverter.INSTANCE.request2Entity(itemControlRequest)
                strategyTemplateItemControlService.save(strategyItemControlEntity)
                strategyItemControlEntity
            }
            val itemEntity = StrategyTemplateItemConverter.INSTANCE.request2Entity(itemRequest).apply {
                templateId = strategyTemplateEntity.id
                projectId = strategyTemplateEntity.projectId
                itemControlId = strategyItemControlEntity?.id
            }
            strategyTemplateItemService.save(itemEntity)
        }
    }


    /**
     * 次接口值更新 templateName
     */
    override fun updateTemplate(strategyTemplateRequest: StrategyTemplateRequest) {
        strategyTemplateRequest.id?.let {
            this.getById(it)?.let { strategyTemplateEntity ->
                strategyTemplateEntity.templateName = strategyTemplateRequest.templateName
                this.updateById(strategyTemplateEntity)
            }
        } ?: run {
            throw ServiceException(ErrorResultCode.ILLEGAL_ARGUMENT.value())
        }
    }

    override fun templateMap(ids: List<Long>): Map<Long, StrategyTemplateEntity> {
        return takeIf { ids.isNotEmpty() }?.let {
            this.list(
                KtQueryWrapper(StrategyTemplateEntity::class.java).`in`(
                    StrategyTemplateEntity::id, ids
                )
            ).associateBy { it.id!! }
        } ?: run {
            mapOf()
        }
    }

    override fun getTemplates(projectId: String): List<StrategyTemplateVo> {
        val template = this.list(
            KtQueryWrapper(StrategyTemplateEntity::class.java).eq(StrategyTemplateEntity::projectId, projectId)
        )
        val itemsMap = strategyTemplateItemService.selectItemByTemplateIds(projectId, template.map { it.id!! })
        val controlIds = itemsMap.values.flatten().filter { it.itemControlId != null }.map { it.itemControlId!! }
        val controlMap = strategyTemplateItemControlService.selectControlByIds(controlIds)
        return template.map {
            StrategyTemplateConverter.INSTANCE.entity2Vo(it).apply {
                items = itemsMap[it.id]?.let { items ->
                    items.map { item ->
                        val vo = StrategyTemplateItemConverter.INSTANCE.entity2Vo(item)
                        vo.itemControl = controlMap[item.itemControlId]?.let { strategyTemplateItemControlVo ->
                            StrategyTemplateItemControlConverter.INSTANCE.entity2Vo(strategyTemplateItemControlVo)
                        }
                        vo
                    }
                } ?: run {
                    listOf()
                }
            }
        }
    }

    override fun getPage(projectId: String?, pageBean: PageBean): IPage<StrategyTemplateVo> {
        val selectPage = this.baseMapper.selectPage(
            Page(
                pageBean.pageNum.toLong(), pageBean.pageSize.toLong()
            ), KtQueryWrapper(StrategyTemplateEntity::class.java).eq(
                // support projectId is null
                projectId != null, StrategyTemplateEntity::projectId, projectId
            ).isNull(projectId == null, StrategyTemplateEntity::projectId)
        )
        val itemsMap =
            //1.4.4 support projectId is null
            strategyTemplateItemService.selectItemByTemplateIdsAll(projectId, selectPage.records.map { it.id!! })

        val controlsMap =
            strategyTemplateItemControlService.selectControlByIds(itemsMap.values.flatMap { itemEntities ->
                itemEntities.filter { it.itemControlId != null }.map { it.itemControlId!! }
            })

        return Page<StrategyTemplateVo>(selectPage.current, selectPage.size, selectPage.total).apply {
            records = selectPage.records.map {
                StrategyTemplateConverter.INSTANCE.entity2Vo(it).apply {
                    items = itemsMap[id]?.let { items ->
                        items.map { item ->
                            StrategyTemplateItemConverter.INSTANCE.entity2Vo(item).apply {
                                itemControl = controlsMap[itemControlId]
                            }
                        }
                    } ?: run {
                        listOf()
                    }
                }
            }
        }
    }

    override fun deleteTemplate(id: Long) {
        //删除前 先校验一下 是否绑定了 天对应的
        val dayExistsUseTemplateFlag = strategyDayTemplateBindMapper.exists(
            KtQueryWrapper(StrategyDayTemplateBindEntity::class.java).eq(
                StrategyDayTemplateBindEntity::projectId, WebUtils.projectId.get()
            ).eq(StrategyDayTemplateBindEntity::templateId, id)
        )
        if (dayExistsUseTemplateFlag) {
            throw ServiceException(ErrorResultCode.STRATEGY_TEMPLATE_IN_USE.value())
        }

        val monthExistsUseTemplateFlag = strategyMonthTemplateBindMapper.exists(
            KtQueryWrapper(StrategyMonthTemplateBindEntity::class.java).eq(
                StrategyMonthTemplateBindEntity::projectId, WebUtils.projectId.get()
            ).eq(StrategyMonthTemplateBindEntity::templateId, id)
        )
        if (monthExistsUseTemplateFlag) {
            throw ServiceException(ErrorResultCode.STRATEGY_TEMPLATE_IN_USE.value())
        }
        //删除模版主体
        this.removeById(id)
    }


    fun validateNoOverlap(items: List<StrategyTemplateItemRequest>): Boolean {
        // 处理时间：将 endTime == 00:00 替换为 23:59:59
        val fixedItems = items.map {
            val fixedEnd = if (it.endTime == LocalTime.MIDNIGHT) LocalTime.of(23, 59, 59) else it.endTime
            TimeRange(it.startTime, fixedEnd)
        }
        // 按 startTime 排序
        val sorted = fixedItems.sortedBy { it.startTime }

        for (i in 1 until sorted.size) {
            val prev = sorted[i - 1]
            val current = sorted[i]
            if (current.startTime.isBefore(prev.endTime)) {
                // 有交叉
                logger.info { "时间冲突:${prev.startTime}-${prev.endTime} 与 ${current.startTime}-${current.endTime} " }
                throw ServiceException(ErrorResultCode.TIME_OVERLAPPED.value())
            }
        }
        return true
    }

    data class TimeRange(val startTime: LocalTime, val endTime: LocalTime)


}