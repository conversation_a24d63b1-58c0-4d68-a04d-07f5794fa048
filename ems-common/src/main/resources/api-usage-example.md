# 文件上传下载API使用说明

## 1. 文件上传API

### 接口地址
```
POST /file/upload
```

### 请求参数
- `file`: MultipartFile - 上传的文件

### 响应示例
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "filePath": "project123/images/uuid-123.jpg",  // 云端项目返回OSS路径
    "fileName": "example.jpg",
    "fileSize": 102400,
    "uploadTime": 1692345678000
  }
}
```

或者（场站项目）：
```json
{
  "code": 200,
  "message": "success", 
  "data": {
    "filePath": "1",  // 场站项目返回文件ID
    "fileName": "example.jpg",
    "fileSize": 102400,
    "uploadTime": 1692345678000
  }
}
```

## 2. 文件下载API

### 接口地址
```
GET /file/download?filePath={filePath}
```

### 请求参数
- `filePath`: String - 文件路径（上传接口返回的filePath值）

### 响应
- 直接返回文件内容，可在浏览器中直接显示图片
- Content-Type根据文件类型自动设置
- Content-Disposition设置为inline，支持浏览器内联显示

### 使用示例

#### 云端项目
1. 上传文件，获得filePath: "project123/images/uuid-123.jpg"
2. 下载文件: `GET /file/download?filePath=project123/images/uuid-123.jpg`

#### 场站项目  
1. 上传文件，获得filePath: "1"
2. 下载文件: `GET /file/download?filePath=1`

### 前端使用示例
```html
<!-- 直接在img标签中使用 -->
<img src="/file/download?filePath=project123/images/uuid-123.jpg" alt="图片">

<!-- 或者通过JavaScript -->
<script>
const filePath = 'project123/images/uuid-123.jpg'; // 从上传接口获得
const downloadUrl = `/file/download?filePath=${encodeURIComponent(filePath)}`;
document.getElementById('myImage').src = downloadUrl;
</script>
```

## 3. 配置说明

通过配置项 `ems.cloud` 控制上传下载行为：
- `ems.cloud=true`: 云端项目，使用OSS存储
- `ems.cloud=false` 或未配置: 场站项目，使用数据库base64存储

## 4. 错误处理

下载接口在出现错误时返回HTTP 404状态码，前端可据此处理异常情况。
